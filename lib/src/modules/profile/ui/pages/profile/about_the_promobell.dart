import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/widgets/no_connect.dart';
import '../../widgets/profile/header_with_back_button.dart';
import '../../widgets/profile/whats_app_promo_card.dart';

class AboutPromobell extends StatelessWidget {
  const AboutPromobell({super.key});

  @override
  Widget build(BuildContext context) {
    return NoConnect(
      child: Scaffold(
        backgroundColor: ColorOutlet.paper,
        body: SelectionArea(
          child: Column(
            children: [
              HeaderWithBackButton(title: 'Sobre o Promobell'),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: <PERSON><PERSON><PERSON>(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      _Section(
                        title: 'O Promobell é confiável?',
                        content:
                            'Sim! O Promobell atua apenas como um canal de divulgação de ofertas, direcionando os usuários para lojas consolidadas e confiáveis como Amazon, Mercado Livre e Magazine Luiza. Todas as transações são realizadas diretamente nos sites oficiais das lojas, garantindo segurança para o comprador.',
                      ),
                      Divider(),
                      _Section(
                        title: 'O Promobell vende produtos?',
                        content:
                            'Não. O Promobell atua exclusivamente como um canal de divulgação de ofertas, direcionando os usuários para sites de terceiros, como Amazon, Mercado Livre e Magazine Luiza. Não realizamos vendas, não intermediamos transações e não garantimos a entrega dos produtos.',
                      ),
                      Divider(),
                      _Section(
                        title: 'O Promobell escolhe os preços das ofertas?',
                        content:
                            'Não. Todos os preços são definidos pelas lojas anunciantes e podem mudar a qualquer momento. Nosso aplicativo apenas divulga as ofertas disponíveis, sem qualquer controle sobre os valores exibidos.',
                      ),
                      Divider(),
                      _Section(
                        title: 'O preço da oferta está diferente no site da loja. O que aconteceu?',
                        content:
                            'Os preços exibidos no Promobell são obtidos diretamente das lojas parceiras e podem sofrer alterações a qualquer momento, sem aviso prévio. Recomendamos sempre verificar o valor final no site do vendedor antes de concluir a compra.',
                      ),
                      Divider(),
                      _Section(
                        title: 'Como reportar uma oferta com informações erradas ou desatualizadas?',
                        content:
                            'Se encontrar uma oferta com preço desatualizado, produto esgotado ou qualquer outra divergência, você pode reportá-la diretamente dentro do próprio aplicativo. Nossa equipe analisará o caso e tomará as medidas necessárias para manter as informações sempre atualizadas.',
                      ),
                      Divider(),
                      _Section(
                        title: 'Comprei um produto divulgado no Promobell, mas ele não foi entregue. O que faço?',
                        content:
                            'Como não realizamos vendas, qualquer questão relacionada a entrega, pagamento ou suporte pós-venda deve ser tratada diretamente com a loja onde a compra foi realizada. Recomendamos verificar o status do pedido no site do vendedor e entrar em contato com o suporte da loja, se necessário.',
                      ),
                      Divider(),
                      _Section(
                        title: 'Por que o Promobell divulga apenas ofertas da Amazon, Mercado Livre e Magazine Luiza?',
                        content:
                            'Escolhemos trabalhar com Amazon, Mercado Livre e Magazine Luiza porque são plataformas de grande credibilidade e alcance nacional, oferecendo segurança na compra, variedade de produtos e boas políticas de atendimento ao cliente.\n\nAlém disso, essas varejistas disponibilizam as melhores ofertas do mercado, com maior potencial de descontos e cupons diariamente, além de uma logística eficiente, garantindo frete grátis em diversas compras e entregas rápidas, muitas vezes em regime full time.\n\nDessa forma, garantimos que nossos usuários tenham acesso a ofertas de lojas confiáveis e com alto volume de transações no mercado, proporcionando uma experiência de compra segura e vantajosa.',
                      ),
                      Divider(),
                      _Section(
                        title: 'Por que o Promobell não tem grupos no WhatsApp ou Telegram?',
                        content:
                            'Optamos por não utilizar grupos de WhatsApp ou Telegram para evitar o envio excessivo de mensagens e garantir uma experiência mais organizada e sem interrupções.\n\nAlém disso, valorizamos sua privacidade. Em grupos, os números dos participantes ficam visíveis para todos, o que pode comprometer a segurança dos seus dados. No Promobell, seguimos diretrizes rigorosas para garantir que suas informações não sejam expostas ou compartilhadas.\n\nPara acompanhar as melhores ofertas de forma segura, ative as notificações push no app ou siga nosso canal oficial no WhatsApp ou instagram.com/promobelloficial',
                      ),
                      Divider(),
                      _Section(
                        title: 'Como entro em contato com o suporte do Promobell?',
                        content:
                            'Se precisar de suporte ou tiver dúvidas que não foram respondidas aqui, entre em contato pelo nosso canal oficial:\n\<EMAIL>',
                      ),
                      SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _Section extends StatefulWidget {
  final String title;
  final String content;

  const _Section({required this.title, required this.content});

  @override
  State<_Section> createState() => _SectionState();
}

class _SectionState extends State<_Section> {
  final emailRegex = RegExp(r'\b[\w\.-]+@[\w\.-]+\.\w{2,4}\b');

  final List<String> boldWords = ["Promobell", "Amazon", "Mercado Livre", "Magazine Luiza"];

  final coloredWords = <String>['canal oficial no WhatsApp', 'instagram.com/promobelloficial'];

  final Map<String, GestureRecognizer> actionWords = {
    'instagram.com/promobelloficial': TapGestureRecognizer()..onTap = () => openPromoBellInstagram(),
    'canal oficial no WhatsApp': TapGestureRecognizer()..onTap = () => openPromoBellWhatsAppChannel(),
  };

  List<TextSpan> _buildTextSpans(String text) {
    List<TextSpan> spans = [];
    text.splitMapJoin(
      RegExp(
        "${emailRegex.pattern}|${(boldWords + coloredWords).map((word) {
          final escaped = RegExp.escape(word);
          return word.contains(' ') ? escaped : '\\b$escaped\\b';
        }).join('|')}",
      ),
      onMatch: (match) {
        String matchedText = match.group(0) ?? "";
        if (emailRegex.hasMatch(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: TextStyle(
                color: ColorOutlet.contentPrimary,
                fontWeight: FontWeight.w400,
                decoration: TextDecoration.underline,
                decorationColor: ColorOutlet.contentPrimary,
                fontFamily: TextPattern().fontFamily,
              ),
              recognizer: TapGestureRecognizer()..onTap = () => launchUrl(Uri.parse("mailto:$matchedText")),
            ),
          );
        } else if (boldWords.contains(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: TextStyle(fontWeight: FontWeight.bold),
              recognizer: actionWords[matchedText],
            ),
          );
        } else if (coloredWords.contains(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: TextStyle(
                color: ColorOutlet.contentPrimary,
                fontWeight: FontWeight.w400,
                decoration: TextDecoration.underline,
                decorationColor: ColorOutlet.contentPrimary,
                fontFamily: TextPattern().fontFamily,
              ),
              recognizer: actionWords[matchedText],
            ),
          );
        }
        return '';
      },
      onNonMatch: (nonMatch) {
        spans.add(TextSpan(text: nonMatch, style: TextStyle(fontWeight: FontWeight.normal)));
        return '';
      },
    );
    return spans;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_buildSectionTitle(widget.title), SizedBox(height: 24), _buildFormattedText(widget.content)],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24),
      child: TextPattern.customText(
        text: title,
        fontSize: 16,
        fontWeightOption: FontWeightOption.bold,
        isSelectable: true,
      ),
    );
  }

  Widget _buildFormattedText(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: RichText(
        text: TextSpan(
          children: _buildTextSpans(text),
          style: TextStyle(
            height: 1.5,
            fontSize: 14,
            color: ColorOutlet.contentSecondary,
            fontFamily: TextPattern().fontFamily,
          ),
        ),
      ),
    );
  }
}

class Divider extends StatelessWidget {
  const Divider({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8, top: 16),
      child: Container(height: 1, width: double.infinity, color: ColorOutlet.contentSecondary.withValues(alpha: 0.1)),
    );
  }
}

Future<void> openPromoBellInstagram() async {
  final url = Uri.parse('https://www.instagram.com/promobelloficial');

  if (await canLaunchUrl(url)) {
    await launchUrl(url, mode: LaunchMode.externalApplication);
  } else {
    throw 'Não foi possível abrir o perfil do instagram.';
  }
}
