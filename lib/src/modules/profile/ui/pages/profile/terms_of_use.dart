import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/custom_pop_scope.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/widgets/no_connect.dart';
import '../../widgets/profile/header_with_back_button.dart';
import '../../widgets/profile/thin_separator.dart';

class TermsOfUse extends StatefulWidget {
  const TermsOfUse({super.key});

  @override
  State<TermsOfUse> createState() => _UsePoliticsState();
}

class _UsePoliticsState extends State<TermsOfUse> {
  final List<String> boldWords = ["Promobell", "Amazon", "Mercado Livre", "Magazine Luiza"];
  final List<String> coloredWords = ["Política de Privacidade"];
  final RegExp emailRegex = RegExp(r'\b[\w\.-]+@[\w\.-]+\.\w+\b');

  final Map<String, VoidCallback> actionWords = {
    "Política de Privacidade": () => Modular.to.pushNamed('/profile/privacy_politics'),
  };

  @override
  Widget build(BuildContext context) {
    return CustomPopScope(
      index: 2,
      child: NoConnect(
        child: Scaffold(
          backgroundColor: ColorOutlet.paper,
          body: SelectionArea(
            child: Column(
              children: [
                HeaderWithBackButton(title: 'Termos de Uso'),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle('Introdução'),
                          _buildFormattedText(
                            'A transparência e a segurança na utilização dos serviços oferecidos pelo Promobell são compromissos fundamentais da nossa empresa. Estes Termos de Uso estabelecem as regras e condições para o acesso e utilização do aplicativo, detalhando as responsabilidades da plataforma e dos usuários.',
                          ),
                          _buildFormattedText(
                            'O Promobell atua exclusivamente como um canal de divulgação de ofertas de terceiros, incluindo Amazon, Mercado Livre e Magazine Luiza, das quais somos afiliados. O aplicativo não realiza vendas, não intermedia transações e não garante a entrega ou o recebimento de produtos adquiridos pelos usuários por meio dos links promocionais divulgados.',
                          ),
                          _buildFormattedText(
                            'Assim, é essencial que os usuários compreendam os limites da nossa atuação e utilizem o serviço de acordo com as disposições aqui estabelecidas.',
                          ),
                          _buildFormattedText(
                            'Ao acessar ou utilizar o Promobell, o usuário declara estar ciente e concorda com as condições descritas neste documento. Caso não concorde com qualquer cláusula dos Termos de Uso, recomendamos que interrompa o uso do serviço.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('1. Independência e Isenção de Responsabilidade'),
                          _buildFormattedText(
                            'As ofertas exibidas no Promobell são de responsabilidade exclusiva das lojas anunciantes. Embora busquemos fornecer informações precisas e atualizadas, não garantimos a exatidão dos dados apresentados, nem nos responsabilizamos por:',
                          ),
                          _buildBulletList([
                            'Alterações de preços realizadas pelos anunciantes;',
                            'Disponibilidade dos produtos divulgados;',
                            'Qualidade, entrega ou quaisquer outros aspectos relacionados à compra;',
                            'Erros ou omissões nas informações fornecidas pelos lojistas.',
                          ]),
                          _buildFormattedText(
                            'O usuário deve verificar todas as condições diretamente na loja anunciante antes de concluir sua compra.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('2. Restrições de Publicação'),
                          _buildFormattedText(
                            'O Promobell reserva-se o direito de não divulgar e remover qualquer oferta que envolva conteúdos proibidos ou que estejam em desacordo com nossas diretrizes. São expressamente vedadas ofertas relacionadas a:',
                          ),
                          _buildBulletList([
                            'Armas de fogo, munições e explosivos;',
                            'Drogas e substâncias controladas;',
                            'Conteúdos adultos, pornográficos, de cunho sexual infantil ou que envolvam exploração de animais;',
                            'Venda de animais;',
                            'Produtos ou serviços que promovam discurso de ódio, violência ou discriminação;',
                            'Qualquer item que infrinja as leis vigentes no Brasil.',
                          ]),
                          _buildFormattedText(
                            'Caso identifiquemos qualquer violação a essas diretrizes, a oferta será removida sem aviso prévio.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('3. Controle e Moderação das Ofertas'),
                          _buildFormattedText(
                            'O Promobell se reserva o direito de editar ou remover qualquer oferta divulgada a qualquer momento, em especial nos seguintes casos:',
                          ),
                          _buildBulletList([
                            'Informações imprecisas ou enganosas;',
                            'Alterações de preços ou indisponibilidade do estoque;',
                            'Erros nas descrições fornecidas pela loja anunciante;',
                            'Ofertas que contrariem as diretrizes estabelecidas.;',
                          ]),
                          _buildFormattedText(
                            'Além disso, caso o usuário identifique ofertas desatualizadas ou inconsistentes, poderá reportá-las diretamente dentro do próprio aplicativo, garantindo assim a precisão das informações exibidas.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('4. Uso de Imagens e Propriedade Intelectual'),
                          _buildFormattedText(
                            'As logomarcas exibidas no Promobell pertencem exclusivamente às lojas anunciantes e seus respectivos fornecedores.',
                          ),
                          _buildFormattedText(
                            'O Promobell não edita, manipula ou altera imagens de produtos. Todas as imagens exibidas correspondem às disponibilizadas pelos lojistas. Algumas imagens podem ser meramente ilustrativas e são de propriedade das empresas responsáveis pelos produtos.',
                          ),
                          _buildFormattedText(
                            'É expressamente proibido copiar, reproduzir ou redistribuir qualquer conteúdo do aplicativo, incluindo textos, imagens e materiais promocionais, sem autorização prévia.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('5. Privacidade e Proteção de Dados'),
                          _buildFormattedText(
                            'O Promobell adota uma política rigorosa de privacidade para garantir a segurança e integridade dos dados pessoais dos usuários.',
                          ),
                          _buildFormattedText(
                            'Não compartilhamos informações pessoais com terceiros, salvo em situações de obrigação legal. Para mais detalhes sobre como os dados dos usuários são tratados, armazenados e protegidos, consulte nossa Política de Privacidade.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('6. Alterações nos termos de uso'),
                          _buildFormattedText(
                            'O Promobell se reserva o direito de atualizar este documento periodicamente para atender exigências legais, regulatórias ou operacionais, garantindo a melhoria contínua da experiência do usuário e da utilização do serviço.',
                          ),
                          _buildFormattedText(
                            'Sempre que houver alterações significativas, os usuários serão notificados via push notification e dentro do próprio aplicativo.',
                          ),
                          _buildFormattedText(
                            'Recomendamos que os usuários revisem periodicamente estes Termos de Uso para se manterem informados sobre eventuais atualizações.',
                          ),
                          ThinSeparator(),
                          _buildSectionTitle('7. Vigência e Disposições Finais'),
                          _buildFormattedText(
                            'A utilização do Promobell implica na leitura, compreensão e concordância com estes Termos de Uso e suas diretrizes.',
                          ),
                          _buildFormattedText(
                            'Se o usuário tiver qualquer dúvida sobre estes Termos de Uso ou precisar de suporte, poderá entrar em contato através do seguinte canal oficial:',
                          ),
                          _buildFormattedText('<EMAIL>'),
                          _buildFormattedText(
                            'Nos comprometemos a responder às solicitações dentro dos prazos estabelecidos pela legislação vigente.',
                          ),
                          _buildFormattedText(
                            'Estes Termos de Uso entram em vigor na data de sua publicação e permanecerão válidos até que uma nova versão seja disponibilizada.',
                          ),
                          ThinSeparator(bottomPadding: 24),
                          TextPattern.customText(text: '© Promobell LTDA'),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 24),
      child: TextPattern.customText(
        text: title,
        fontSize: 20,
        fontWeightOption: FontWeightOption.bold,
        isSelectable: true,
      ),
    );
  }

  Widget _buildFormattedText(String text, {double bottomPadding = 24, double topPadding = 0}) {
    return Padding(
      padding: EdgeInsets.only(top: topPadding, bottom: bottomPadding),
      child: SelectableText.rich(
        TextSpan(
          children: _buildTextSpans(text),
          style: TextStyle(
            height: 1.5,
            fontSize: 14,
            color: ColorOutlet.contentSecondary,
            fontFamily: TextPattern().fontFamily,
          ),
        ),
      ),
    );
  }

  Widget _buildBulletList(List<String> items) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items.map((item) => _buildFormattedText('• $item', bottomPadding: 0)).toList(),
      ),
    );
  }

  List<TextSpan> _buildTextSpans(String text) {
    List<TextSpan> spans = [];
    text.splitMapJoin(
      RegExp(
        "${emailRegex.pattern}|${(boldWords + coloredWords).map((word) {
          final escaped = RegExp.escape(word);
          return word.contains(' ') ? escaped : '\\b$escaped\\b';
        }).join('|')}",
      ),
      onMatch: (match) {
        String matchedText = match.group(0) ?? "";
        if (emailRegex.hasMatch(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: TextStyle(
                color: ColorOutlet.contentPrimary,
                fontWeight: FontWeight.w400,
                fontFamily: TextPattern().fontFamily,
                decoration: TextDecoration.underline,
                decorationColor: ColorOutlet.contentPrimary,
              ),
              recognizer: TapGestureRecognizer()..onTap = () => launchUrl(Uri.parse("mailto:$matchedText")),
            ),
          );
        } else if (boldWords.contains(matchedText)) {
          spans.add(TextSpan(text: matchedText, style: const TextStyle(fontWeight: FontWeight.bold)));
        } else if (coloredWords.contains(matchedText)) {
          spans.add(
            TextSpan(
              text: matchedText,
              style: TextStyle(
                color: ColorOutlet.contentPrimary,
                fontWeight: FontWeight.w400,
                decoration: TextDecoration.underline,
                decorationColor: ColorOutlet.contentPrimary,
                fontFamily: TextPattern().fontFamily,
              ),
              recognizer: TapGestureRecognizer()..onTap = actionWords[matchedText],
            ),
          );
        }
        return '';
      },
      onNonMatch: (nonMatch) {
        spans.add(
          TextSpan(
            text: nonMatch,
            style: TextStyle(fontFamily: TextPattern().fontFamily, fontWeight: FontWeight.normal),
          ),
        );
        return '';
      },
    );
    return spans;
  }
}
