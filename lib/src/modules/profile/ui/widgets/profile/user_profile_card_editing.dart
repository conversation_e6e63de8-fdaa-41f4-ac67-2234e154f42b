import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/custom_radio_tile.dart';
import 'package:promobell/src/modules/profile/ui/widgets/profile/speech_bubble.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/custom_snack_bar.dart';
import '../../../../../components/input_field_pattern.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../categories/ui/widgets/detail/custom_dialog.dart';
import '../../../controllers/profile_controller.dart';

class UserProfileCardEditing extends StatefulWidget {
  final String? name;
  final String? surname;
  final String? birthDate;
  final String email;
  final String? joinedDate;
  final VoidCallback onPressed;
  final VoidCallback onTapImage;

  const UserProfileCardEditing({
    required this.onPressed,
    required this.name,
    required this.email,
    required this.joinedDate,
    required this.onTapImage,
    required this.surname,
    required this.birthDate,
    super.key,
  });

  @override
  State<UserProfileCardEditing> createState() =>
      _UserProfileCardEditingState();
}

class _UserProfileCardEditingState
    extends State<UserProfileCardEditing> {
  final controller = Modular.get<ProfileController>();
  final birthDateFormatter = MaskTextInputFormatter(
    mask: '##/##/####',
    filter: {"#": RegExp(r'[0-9]')},
  );

  @override
  void initState() {
    super.initState();
    // Garante que os controllers sejam inicializados com os dados atuais
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeControllers();
    });
  }

  void _initializeControllers() {
    // Inicializa os controllers com os dados passados via widget
    if (widget.name != null && widget.name!.isNotEmpty) {
      controller.nameController?.text = widget.name!;
      controller.userName = widget.name;
    }

    if (widget.surname != null && widget.surname!.isNotEmpty) {
      controller.surnameController?.text = widget.surname!;
      controller.userSurname = widget.surname;
    }

    if (widget.birthDate != null && widget.birthDate!.isNotEmpty) {
      try {
        // Converte de ISO para formato dd/MM/yyyy
        final parsedDate = DateTime.parse(widget.birthDate!);
        final formattedDate =
            '${parsedDate.day.toString().padLeft(2, '0')}/${parsedDate.month.toString().padLeft(2, '0')}/${parsedDate.year}';
        controller.birthDateController?.text = formattedDate;
        controller.birthDate = widget.birthDate;
      } catch (e) {
        // Se não conseguir parsear, usa o valor direto
        controller.birthDateController?.text = widget.birthDate!;
      }
    }

    // Garante que o email seja definido
    controller.userEmail = widget.email;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Form(
          key: controller.formKeyProfile,
          child: SingleChildScrollView(
            physics: NeverScrollableScrollPhysics(),
            child: Column(
              spacing: 16,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: TextPattern.customText(
                        text: 'Nome',
                        fontWeightOption: FontWeightOption.bold,
                      ),
                    ),
                    if (controller.userName == null ||
                        controller.userName == '')
                      Padding(
                        padding: const EdgeInsets.only(right: 24),
                        child: SvgPicture.asset(
                          SvgIcons.feedbackCautionFilled,
                          height: 24,
                          width: 24,
                        ),
                      ),
                  ],
                ),
                if (controller.userName == null ||
                    controller.userName == '')
                  SpeechBubble(
                    text:
                        'Isso nos ajuda a deixar tudo mais personalizado pra você.',
                    color: ColorOutlet.feedbackWarning,
                  ),

                InputFieldPattern(
                  controller: controller.nameController,
                  hintText: 'Nome',
                  validator:
                      (value) => controller.validator(
                        value: value,
                        name: controller.nameController,
                      ),
                ),
                InputFieldPattern(
                  controller: controller.surnameController,
                  hintText: 'Sobrenome (opcional)',
                  validator:
                      (value) =>
                          controller.validarCampoSobrenome(value),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: TextPattern.customText(
                        text: 'Nascimento',
                        fontWeightOption: FontWeightOption.bold,
                      ),
                    ),
                    if (controller.birthDate == null ||
                        controller.birthDate == '')
                      Padding(
                        padding: const EdgeInsets.only(right: 24),
                        child: SvgPicture.asset(
                          SvgIcons.feedbackCautionFilled,
                          height: 24,
                          width: 24,
                        ),
                      ),
                  ],
                ),
                if (controller.birthDate == null ||
                    controller.birthDate == '')
                  SpeechBubble(
                    text:
                        'Com essa informação, podemos recomendar ofertas adequadas pra você.',
                    color: ColorOutlet.feedbackWarning,
                  ),
                InputFieldPattern(
                  controller: controller.birthDateController,
                  hintText: '01/01/2000',
                  inputFormatters: [birthDateFormatter],
                  validator: controller.validateBirthDate,
                  textColor: ColorOutlet.contentSecondary,
                ),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: TextPattern.customText(
                        text: 'Pronomes',
                        fontWeightOption: FontWeightOption.bold,
                      ),
                    ),
                    if (controller.gender == null ||
                        controller.gender == '')
                      Padding(
                        padding: const EdgeInsets.only(right: 24),
                        child: SvgPicture.asset(
                          SvgIcons.feedbackCautionFilled,
                          height: 24,
                          width: 24,
                        ),
                      ),
                  ],
                ),
                if (controller.gender == null ||
                    controller.gender == '')
                  SpeechBubble(
                    text:
                        'Assim, a gente usa os pronomes certos e fala com você do seu jeito.',
                    color: ColorOutlet.feedbackWarning,
                  ),
                InputFieldPattern(
                  suffixIconInput: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SvgPicture.asset(SvgIcons.arrowTipDown),
                  ),
                  controller: controller.genderController,
                  hintText: 'Selecionar',
                  textColor: ColorOutlet.contentSecondary,
                  readOnly: true,
                  onTap:
                      () => showGenderDialog(
                        context,
                        selectedGender: controller.gender,
                        onSelect: (value) {
                          controller.gender = value;
                          controller.genderController.text = value;
                        },
                      ),
                ),

                Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: TextPattern.customText(
                    text: 'E-mail',
                    fontWeightOption: FontWeightOption.bold,
                  ),
                ),
                Stack(
                  children: [
                    InputFieldPattern(
                      initialValue: widget.email,
                      textColor: ColorOutlet.contentGhost,
                      enabled: false,
                      suffixIcon: SvgPicture.asset(
                        SvgIcons.feedbackInfo,
                      ),
                      suffixIconTap: () async {
                        String plataforma = await controller
                            .getPlataformaAutenticacao(widget.email);
                        if (context.mounted) {
                          CustomDialog.show(
                            context,
                            onConfirm: () {},
                            onCancel: () {},
                            buttonOnly: true,
                            title: 'Email cadastrado',
                            message:
                                'Seu cadastro foi feito utilizando sua conta $plataforma. Por isso, o email associado não pode ser alterado diretamente no nosso app.\n\nSe precisar usar outro email, será necessário criar uma nova conta com a credencial desejada.',
                          );
                        }
                      },
                    ),
                  ],
                ),
                Align(
                  alignment: Alignment.center,
                  child: TextButton(
                    onPressed: () {
                      CustomDialog.show(
                        context,
                        textOnConfirm: 'Excluir conta',
                        onConfirm:
                            () => Modular.to.pushReplacementNamed(
                              '/profile/delete_account',
                            ),
                        onCancel: () {},
                        buttonOnly: false,
                        title: 'Excluir conta',
                        message:
                            'Ao excluir sua conta, todos os seus dados serão removidos permanentemente, e não será possível recuperar o acesso.\n\nCaso queira utilizar o Promobell novamente no futuro, será necessário criar uma nova conta do zero.\n\nTem certeza de que deseja continuar?',
                      );
                    },
                    child: TextPattern.customText(
                      text: 'Excluir conta',
                      fontWeightOption: FontWeightOption.regular,
                      color: ColorOutlet.feedbackError,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 24),
                  child: buildActionButtons(context),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildActionButtons(BuildContext context) {
    return Row(
      spacing: 16,
      children: [
        SizedBox(
          height: 44,
          child: TextButton(
            onPressed: widget.onPressed,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: TextPattern.customText(text: 'Cancelar'),
            ),
          ),
        ),
        Expanded(
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              minimumSize: Size(
                MediaQuery.of(context).size.width * .48,
                44,
              ),
              backgroundColor: ColorOutlet.contentPrimary,
              overlayColor: ColorOutlet.systemBorderDisabled
                  .withValues(alpha: 0.3),
            ),
            onPressed:
                controller.loadSaving
                    ? null
                    : () async {
                      controller.loadSaving = true;
                      try {
                        await controller.updateProfile();

                        controller.loadSaving = false;

                        // Exibir SnackBar de sucesso
                        if (context.mounted) {
                          CustomSnackBar.show(
                            context: context,
                            message: "Seus ajustes foram salvos.",
                            icon: SvgIcons.feedbackCheck,
                          );
                        }

                        // Resetar o formulário após 2 segundos apenas se salvou com sucesso
                        await Future.delayed(
                          const Duration(seconds: 2),
                          () {
                            controller.formKeyProfile.currentState
                                ?.reset();
                          },
                        );
                      } catch (e) {
                        controller.loadSaving = false;

                        // Exibir SnackBar de erro
                        if (context.mounted) {
                          CustomSnackBar.show(
                            context: context,
                            message:
                                "Ocorreu um erro ao salvar seus ajustes. Tente novamente mais tarde.",
                            icon:
                                SvgIcons
                                    .feedbackCaution, // Supondo que você tenha um ícone de erro
                          );
                        }
                      }
                    },
            child:
                controller.loadSaving
                    ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 1.5,
                      ),
                    )
                    : TextPattern.customText(
                      text: 'Salvar',
                      color: ColorOutlet.contentTertiary,
                    ),
          ),
        ),
      ],
    );
  }
}

Future<void> showGenderDialog(
  BuildContext context, {
  required String? selectedGender,
  required Function(String) onSelect,
}) async {
  final options = [
    'Como ele',
    'Como ela',
    'De forma neutra (sem usar ele/ela)',
  ];

  String? tempGender = selectedGender;

  await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
    ),
    builder: (context) {
      final bottom = MediaQuery.of(context).viewInsets.bottom;

      return Padding(
        padding: EdgeInsets.fromLTRB(24, 24, 24, bottom + 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: ColorOutlet.contentGhost,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 24),
            TextPattern.customText(
              text: 'Pronomes de gênero',
              fontSize: 20,
              fontWeightOption: FontWeightOption.semiBold,
            ),
            const SizedBox(height: 8),
            TextPattern.customText(
              text:
                  'Selecione como gostaria que a gente se referisse a você. Assim, a gente usa os pronomes certos e fala com você do seu jeito.',
              fontSize: 14,
              color: ColorOutlet.contentSecondary,
            ),
            const SizedBox(height: 24),
            ...options.map((option) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: CustomRadioTile(
                  value: option,
                  groupValue: tempGender,
                  onChanged: (val) {
                    tempGender = val;
                    onSelect(
                      val ??
                          '', // Certifique-se de que o valor não seja nulo
                    ); // Salva no controller ou estado externo
                    Navigator.pop(context); // Fecha após seleção
                  },
                  text: option,
                ),
              );
            }),
          ],
        ),
      );
    },
  );
}
